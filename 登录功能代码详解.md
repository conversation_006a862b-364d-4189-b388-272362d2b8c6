# 登录功能代码详解

## 1. login.vue - 普通用户登录页面

### 模板部分 (Template)
```vue
<template>
    <div class="login-container" >  <!-- 登录页面的最外层容器，用于页面布局 -->
        <el-card class="box-card">  <!-- Element UI的卡片组件，提供阴影和边框效果 -->
            <div class="login-body">  <!-- 登录表单的内容区域 -->
                <div class="login-title" @click="toIndex" >校园交易平台</div>  <!-- 页面标题，@click绑定点击事件跳转首页 -->
                <el-form ref="form" :model="userForm">  <!-- Element UI表单组件，ref用于获取表单引用，:model绑定表单数据 -->
                    <el-input placeholder="请输入账号..." v-model="userForm.accountNumber" class="login-input">  <!-- 账号输入框，v-model双向绑定数据 -->
                        <template slot="prepend">   <!-- Element UI输入框的前置插槽，用于显示图标 -->
                            <div class="el-icon-user-solid"></div>  <!-- Element UI用户图标 -->
                        </template>
                    </el-input> 
                    <el-input placeholder="请输入密码..." v-model="userForm.userPassword" class="login-input"  <!-- 密码输入框 -->
                              @keyup.enter.native="login"  show-password>  <!-- @keyup.enter.native监听回车键触发登录，show-password显示密码切换按钮 -->
                        <template slot="prepend">  <!-- 密码框前置图标插槽 -->
                            <div class="el-icon-lock"></div>  <!-- Element UI锁图标 -->
                        </template>
                    </el-input>
                    <div class="login-submit" >  <!-- 按钮容器 -->
                        <el-button type="primary" @click="login">登录</el-button>  <!-- 主要按钮样式，绑定登录方法 -->
                        <el-button type="warning" autocomplete="off" @click="$router.push('/sign-in')" style="margin-left: 20px">注册</el-button>  <!-- 警告样式按钮，$router.push进行路由跳转 -->
                    </div>
                    <div class="other-submit">  <!-- 其他操作链接容器 -->
                        <router-link to="/login-admin" class="sign-in-text">管理员登录</router-link>  <!-- Vue Router的链接组件，声明式导航 -->
                    </div>
                </el-form>
            </div>
        </el-card>
    </div>
</template>
```

### 脚本部分 (Script)
```javascript
<script>
    export default {  // ES6模块导出，Vue组件的标准写法
        name: "login",  // 组件名称，用于Vue开发工具调试
        data() {  // Vue组件的数据函数，返回响应式数据对象
            return {
                userForm: {  // 用户登录表单数据对象
                    accountNumber: '',  // 用户账号，空字符串作为初始值
                    userPassword: ''    // 用户密码，空字符串作为初始值
                }
            };
        },

        methods: {  // Vue组件的方法对象，定义组件的行为
            login() {  // 登录方法，处理用户登录逻辑
                console.log('登录按钮被点击');  // 控制台输出，用于调试
                console.log('登录参数:', this.userForm);  // 输出登录参数，this指向当前Vue实例
                this.$api.userLogin({  // 调用全局API方法，$api是挂载在Vue原型上的API对象
                    accountNumber: this.userForm.accountNumber,  // 传递账号参数
                    userPassword: this.userForm.userPassword    // 传递密码参数
                }).then(res => {  // Promise的then方法处理成功响应，res是服务器返回的数据
                    console.log('登录响应:', res);  // 输出服务器响应用于调试
                    if (res.status_code === 1) {  // 判断服务器返回的状态码，1表示成功
                        res.data.signInTime=res.data.signInTime.substring(0,10);  // 截取注册时间的前10位（日期部分）
                        this.$globalData.userInfo = res.data;  // 将用户信息保存到全局数据对象中
                        this.$router.replace({path: '/index'});  // 使用replace方法跳转到首页，不会在历史记录中留下当前页面
                    } else {  // 登录失败的处理
                        this.$message.error(res.msg);  // 使用Element UI的消息组件显示错误信息
                    }
                }).catch(error => {  // Promise的catch方法处理请求失败
                    console.error('登录请求失败:', error);  // 输出错误信息到控制台
                    this.$message.error('登录请求失败，请检查网络连接');  // 显示网络错误提示
                });
            },
            toIndex() {  // 跳转到首页的方法
                this.$router.replace({path: '/index'});  // 路由跳转到首页
            }
        }
    }
</script>
```

### 样式部分 (Style)
```css
<style scoped>  /* scoped表示样式只作用于当前组件，避免样式污染 */
    .login-container {  /* 登录容器样式 */
        display: flex;  /* 弹性布局 */
        justify-content: center;  /* 水平居中 */
        align-items: center;  /* 垂直居中 */
        height: 100vh;  /* 高度为视口高度的100% */
        width: 100%;  /* 宽度100% */
    }

    .login-body {  /* 登录表单主体样式 */
        padding: 30px;  /* 内边距30像素 */
        width: 300px;  /* 固定宽度300像素 */
        height: 100%;  /* 高度100% */
    }

    .login-title {  /* 标题样式 */
        padding-bottom: 30px;  /* 下内边距 */
        text-align: center;  /* 文字居中 */
        font-weight: 600;  /* 字体粗细 */
        font-size: 20px;  /* 字体大小 */
        color: #409EFF;  /* Element UI主题色 */
        cursor: pointer;  /* 鼠标悬停时显示手型光标 */
    }

    .login-input {  /* 输入框样式 */
        margin-bottom: 20px;  /* 下外边距 */
    }

    .login-submit {  /* 提交按钮容器样式 */
        margin-top: 20px;  /* 上外边距 */
        display: flex;  /* 弹性布局 */
        justify-content: center;  /* 水平居中 */
    }

    .sign-in-text {  /* 注册链接文字样式 */
        color: #409EFF;  /* Element UI主题色 */
        font-size: 16px;  /* 字体大小 */
        text-decoration: none;  /* 去除下划线 */
        line-height:28px;  /* 行高 */
    }
    
    .other-submit{  /* 其他操作容器样式 */
        display:flex;  /* 弹性布局 */
        justify-content: space-between;  /* 两端对齐 */
        margin-top: 30px;  /* 上外边距 */
        margin-left: 200px;  /* 左外边距，用于右对齐效果 */
    }
</style>
```

## 2. login-admin.vue - 管理员登录页面

### 模板部分 (Template)
```vue
<template>
    <div class="login-container">  <!-- 管理员登录页面容器 -->
        <el-card class="box-card">  <!-- Element UI卡片组件 -->
            <div class="login-body">  <!-- 登录表单主体 -->
                <div class="login-title">后台管理</div>  <!-- 管理员登录页面标题 -->
                <el-form ref="form" :model="userForm">  <!-- 管理员登录表单，绑定userForm数据 -->
                    <el-input placeholder="请输入管理员账号" v-model="userForm.accountNumber" class="login-input">  <!-- 管理员账号输入框 -->
                        <template slot="prepend">  <!-- 前置图标插槽 -->
                            <div class="el-icon-user-solid"></div>  <!-- 用户图标 -->
                        </template>
                    </el-input>
                    <el-input placeholder="请输入管理员密码" v-model="userForm.adminPassword" class="login-input"  <!-- 管理员密码输入框，注意这里是adminPassword -->
                              @keyup.enter.native="login"  show-password>  <!-- 回车登录和密码显示切换 -->
                        <template slot="prepend">  <!-- 前置图标插槽 -->
                            <div class="el-icon-lock"></div>  <!-- 锁图标 -->
                        </template>
                    </el-input>
                    <div class="login-submit" style="margin-top: 20px"  >  <!-- 按钮容器，内联样式设置上边距 -->
                        <el-button type="primary" @click="login">登录</el-button>  <!-- 登录按钮 -->
                        <el-button type="warning" autocomplete="off" @click="$router.push('/login')" style="margin-left: 20px">返回前台</el-button>  <!-- 返回普通用户登录页面 -->
                    </div>
                </el-form>
            </div>
        </el-card>
    </div>
</template>
```
